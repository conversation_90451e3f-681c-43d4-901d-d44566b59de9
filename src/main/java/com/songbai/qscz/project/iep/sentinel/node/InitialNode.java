package com.songbai.qscz.project.iep.sentinel.node;

import cn.hutool.core.util.StrUtil;
import cn.hutool.v7.json.JSONObject;
import cn.hutool.v7.json.JSONUtil;
import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.songbai.qscz.project.qscz.aba.dto.*;
import com.songbai.qscz.project.qscz.aba.service.*;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 流程初始化节点
 *
 * <AUTHOR>
 * @since 2025-08-26 11:09
 */
@Slf4j
public class InitialNode implements NodeAction {

    private final AbaPlanService abaPlanService;
    private final YcxChildrenService childrenService;
    private final AbaDailyGoalDataService dailyGoalDataService;
    private final AbaProjectService projectService;
    private final AbaPlanProjectGoalHistoryService planProjectGoalHistoryService;

    public InitialNode (YcxChildrenService childrenService, AbaPlanService abaPlanService, AbaDailyGoalDataService dailyGoalDataService, AbaProjectService projectService, AbaPlanProjectGoalHistoryService planProjectGoalHistoryService) {
        this.abaPlanService = abaPlanService;
        this.childrenService = childrenService;
        this.dailyGoalDataService = dailyGoalDataService;
        this.projectService = projectService;
        this.planProjectGoalHistoryService = planProjectGoalHistoryService;
    }


    @Override
    public Map<String, Object> apply (OverAllState overAllState) throws Exception {
        log.info("initial node is running.");
        String input = overAllState.value ("input", "{}");
        if(StrUtil.isBlank(input)){
            log.error ("initial node input is blank.");
            // TODO 报错方式待定
            throw new Exception ("initial node input is blank.");
        }
        JSONObject inputJson = JSONUtil.parseObj (input);
        HashMap<String, Object> result = new HashMap<> ();

        // 儿童信息
        YcxChildrenDto childInfo = childrenService.getChildInfo (inputJson.getInt ("childId"));
        result.put ("childInfo", childInfo);

        // 完整计划详细信息（包含正在进行的康复项目，通过的康复项目，挂起暂停的康复项目）
        Integer planId = inputJson.getInt ("planId");
        AbaPlanDto planDetail = abaPlanService.getPlanDetailByPlanId (planId);
        result.put ("planDetail", planDetail);

        //需调整的项目近期训练的记录数据
        List<AbaDailyDataDto> planProjectDailyTrainingDetail = dailyGoalDataService.getDailyDataList (planId, inputJson.getInt ("planProjectGoalId"));
        result.put ("planProjectDailyTrainingDetail", planProjectDailyTrainingDetail);

        // 需调整的项目的过往调整历史：
        // TODO 是将历史与每日数据结合，还是作为一个独立信息给出提高AI注意力
        List<AbaPlanProjectGoalHistoryDto> adjustHistoryList = planProjectGoalHistoryService.getAdjustHistoryList (inputJson.getInt ("planProjectGoalId"));
        result.put ("planProjectGoalAdjustHistory", adjustHistoryList);

        //该项目的完整训练信息（包含全部的短期目标，建议小目标，建议辅助方式等）
        AbaProjectDto projectDetail = projectService.getProjectDetail (inputJson.getInt ("projectId"));
        result.put ("projectDetail", projectDetail);

        // TODO 将每次的内容精简总结后作为长期记忆，后续增加长期记忆的信息

        return result;
    }
}
