package com.songbai.qscz.project.iep.sentinel;

import cn.hutool.v7.json.JSONObject;
import cn.hutool.v7.json.JSONUtil;
import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.EdgeAction;
import lombok.extern.slf4j.Slf4j;

/**
 * 决策路由调度
 *
 * <AUTHOR>
 * @since 2025-08-26 18:56
 */
@Slf4j
public class DecisionDispatchEdge implements EdgeAction {

    /**
     * Applies this action to the given agent state.
     *
     * @param state the agent state
     * @return a result of the action
     * @throws Exception if an error occurs during the action
     */
    @Override
    public String apply (OverAllState state) throws Exception {
        String decision = state.value ("decision", "{}");
        // 去除 markdown code block
        decision = decision.trim ();
        if (decision.startsWith ("```json")) {
            decision = decision.replaceFirst ("^```json", "").trim ();
        }
        if (decision.startsWith ("```")) {
            decision = decision.replaceFirst ("^```", "").trim ();
        }
        if (decision.endsWith ("```")) {
            decision = decision.replaceAll ("```$", "").trim ();
        }
        // 解析 JSON 并提取 recommend 字段
        try {
            JSONObject decisionJson = JSONUtil.parseObj (decision);
            if (! decisionJson.getBool ("adjust")) {
                return "NONE";
            }
            String recommend = decisionJson.getStr ("recommend");
            log.debug ("提取到的推荐策略: {}", recommend);
            return recommend;
        } catch (Exception e) {
            log.warn ("解析决策结果失败，使用默认策略: {}", decision, e);
            return "NONE";
        }
    }
}
