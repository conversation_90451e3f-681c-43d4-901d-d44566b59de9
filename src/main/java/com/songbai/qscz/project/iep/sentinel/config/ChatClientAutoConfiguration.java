package com.songbai.qscz.project.iep.sentinel.config;

import com.songbai.qscz.project.iep.sentinel.prompt.SentinelAgentPrompts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * Agents配置
 *
 * <AUTHOR>
 * @since 2025-08-26 9:39
 */
@Slf4j
@Configuration
public class ChatClientAutoConfiguration {

    @Autowired
    private ApplicationContext context;


    /**
     * Return the tool name array that have corresponding beans.
     */
    private String[] getAvailableTools(String... toolNames) {
        return Arrays.stream(toolNames).filter(context::containsBean).toArray(String[]::new);
    }

    /**
     * 决策智能体
     * @param decisionChatClientBuilder
     * @return
     */
    @Bean
    public ChatClient decisionChatClient (ChatClient.Builder decisionChatClientBuilder) {
        return decisionChatClientBuilder.defaultSystem(SentinelAgentPrompts.DECISION_SYSTEM_PROMPT).build ();
            // .defaultAdvisors (new ReasoningContentAdvisor (1)).build();
    }

    /**
     * 项目调整智能体
     * @param projectChatClientBuilder
     * @return
     */
    @Bean
    public ChatClient projectChatClient (ChatClient.Builder projectChatClientBuilder) {
        return projectChatClientBuilder.defaultSystem(SentinelAgentPrompts.PROJECT_SYSTEM_PROMPT).build();
    }

    /**
     * 短期目标调整智能体
     * @param goalChatClientBuilder
     * @return
     */
    @Bean
    public ChatClient goalChatClient (ChatClient.Builder goalChatClientBuilder) {
        return goalChatClientBuilder.defaultSystem(SentinelAgentPrompts.GOAL_SYSTEM_PROMPT).build();
    }

    /**
     * 小目标调整智能体
     * @param targetChatClientBuilder
     * @return
     */
    @Bean
    public ChatClient targetChatClient (ChatClient.Builder targetChatClientBuilder) {
        return targetChatClientBuilder.defaultSystem(SentinelAgentPrompts.TARGET_SYSTEM_PROMPT).build();
    }

    /**
     * 辅助方式调整智能体
     * @param supportChatClientBuilder
     * @return
     */
    @Bean
    public ChatClient supportChatClient (ChatClient.Builder supportChatClientBuilder) {
        return supportChatClientBuilder.defaultSystem(SentinelAgentPrompts.SUPPORT_SYSTEM_PROMPT).build();
    }
}
