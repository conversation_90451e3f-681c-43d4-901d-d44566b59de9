package com.songbai.qscz.project.qscz.aba.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.songbai.qscz.project.qscz.aba.dto.AbaPlanDto;
import com.songbai.qscz.project.qscz.aba.model.AbaPlan;


/**
 * <AUTHOR>
 */
public interface AbaPlanService extends IService<AbaPlan> {

    /**
     * 根据儿童ID获取计划详情(包含已通过和已挂起的项目)
     * @param planId 计划ID
     * @return
     */
    AbaPlanDto getPlanDetailByPlanId (Integer planId);
}
