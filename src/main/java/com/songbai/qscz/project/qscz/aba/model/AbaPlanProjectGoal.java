package com.songbai.qscz.project.qscz.aba.model;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 计划项目目标表(AbaPlanProjectGoal)表实体类
 *
 * <AUTHOR>
 * @since 2025-08-26 14:33:38
 */
@Data
@Accessors (chain = true)
@TableName ("aba_plan_project_goal")
public class AbaPlanProjectGoal {

    /**
     * 主键
     */
    @TableField ("id")
    private Integer id;

    /**
     * 机构ID
     */
    @TableField ("org_id")
    private Integer orgId;

    /**
     * 儿童ID
     */
    @TableField ("child_id")
    private Integer childId;

    /**
     * 计划ID
     */
    @TableField ("plan_id")
    private Integer planId;

    /**
     * 领域ID
     */
    @TableField ("domain_id")
    private Integer domainId;

    /**
     * 计划项目ID
     */
    @TableField ("plan_project_id")
    private Integer planProjectId;

    /**
     * 项目ID
     */
    @TableField ("project_id")
    private Integer projectId;

    /**
     * 项目名称
     */
    @TableField ("project_name")
    private String projectName;

    /**
     * 短期目标ID
     */
    @TableField ("short_goal_id")
    private Integer shortGoalId;

    /**
     * 短期目标名称
     */
    @TableField ("short_goal_name")
    private String shortGoalName;

    /**
     * 辅助方式
     */
    @TableField ("assist_method")
    private String assistMethod;

    /**
     * 目标(Json格式存储)
     */
    @TableField ("target")
    private String target;

    /**
     * 开始时间
     */
    @TableField ("start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField ("end_time")
    private Date endTime;

    /**
     * 记录方式（1回合制 2试探 3分步骤 ）
     */
    @TableField ("record_type")
    private Integer recordType;

    /**
     * 操作类型(0创建 1调整 2通过 3挂起)
     */
    @TableField ("opt_type")
    private Integer optType;

    /**
     * 状态((1进行中 2通过 3挂起)
     */
    @TableField ("status")
    private Integer status;

    /**
     * 创建人
     */
    @TableField ("create_user")
    private String createUser;

    /**
     * 更新人
     */
    @TableField ("update_user")
    private String updateUser;

    /**
     * 挂起原因
     */
    @TableField ("remarks")
    private String remarks;

}
