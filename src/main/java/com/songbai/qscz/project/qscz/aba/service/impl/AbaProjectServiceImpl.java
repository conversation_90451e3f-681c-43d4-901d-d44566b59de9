package com.songbai.qscz.project.qscz.aba.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.songbai.qscz.project.qscz.aba.dto.AbaProjectDto;
import com.songbai.qscz.project.qscz.aba.dto.AbaProjectGoalDto;
import com.songbai.qscz.project.qscz.aba.mapper.AbaProjectGoalMapper;
import com.songbai.qscz.project.qscz.aba.mapper.AbaProjectMapper;
import com.songbai.qscz.project.qscz.aba.model.AbaProject;
import com.songbai.qscz.project.qscz.aba.model.AbaProjectGoal;
import com.songbai.qscz.project.qscz.aba.service.AbaProjectService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * (AbaProject)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-27 10:22:32
 */

@Service ("abaProjectService")
public class AbaProjectServiceImpl extends ServiceImpl<AbaProjectMapper,AbaProject> implements AbaProjectService {

    @Resource
    private AbaProjectMapper abaProjectMapper;
    @Resource
    private AbaProjectGoalMapper abaProjectGoalMapper;


    /**
     * 获取项目详细信息
     *
     * @param projectId 项目ID
     * @return
     */
    @Override
    public AbaProjectDto getProjectDetail (Integer projectId) {
        AbaProject abaProject = abaProjectMapper.selectById (projectId);
        AbaProjectDto projectDto = BeanUtil.copyProperties (abaProject, AbaProjectDto.class);

        LambdaQueryWrapper<AbaProjectGoal> goalQueryWrapper = new LambdaQueryWrapper<AbaProjectGoal> ()
            .eq (AbaProjectGoal::getProjectId, projectId);
        List<AbaProjectGoal> projectGoalList = abaProjectGoalMapper.selectList (goalQueryWrapper);

        projectDto.setProjectGoalList (BeanUtil.copyToList (projectGoalList, AbaProjectGoalDto.class));

        return projectDto;
    }
}
