package com.songbai.qscz.project.qscz.aba.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AbaPlanProjectGoalHistoryDto implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 儿童ID
     */
    private Integer childId;

    /**
     * 计划ID
     */
    private Integer planId;

    /**
     * 领域ID
     */
    private Integer domainId;

    /**
     * 计划项目ID
     */
    private Integer planProjectId;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 计划项目目标ID
     */
    private Integer planProjectGoalId;

    /**
     * 短期目标ID
     */
    private Integer shortGoalId;

    /**
     * 短期目标名称
     */
    private String shortGoalName;

    /**
     * 辅助方式
     */
    private String assistMethod;

    /**
     * 记录方式（1回合制 2试探 3分步骤 ）
     */
    private Integer recordType;

    /**
     * 操作类型(0创建 1调整 2通过 3挂起)
     */
    private Integer optType;

    /**
     * 挂起原因
     */
    private String remarks;

    /**
     * 目标(Json格式存储)
     */
    private String target;

    /**
     * 修改批次
     */
    private Integer batchNo;

    /**
     * 创建时间
     */
    private String createTime;

    private static final long serialVersionUID = 1L;

}
