package com.songbai.qscz.project.qscz.aba.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@TableName("aba_plan_project")
public class AbaPlanProject implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 机构ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 儿童ID
     */
    @TableField("child_id")
    private Integer childId;

    /**
     * 计划ID
     */
    @TableField("plan_id")
    private Integer planId;

    /**
     * 领域ID
     */
    @TableField("domain_id")
    private Integer domainId;

    /**
     * 领域名称
     */
    @TableField("domain_name")
    private String domainName;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 项目名称
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 项目阶段
     */
    @TableField("project_level")
    private Integer projectLevel;

    /**
     * 长期目标
     */
    @TableField("long_goal")
    private String longGoal;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 状态(1进行中 2通过 3挂起)
     */
    @TableField("status")
    private Integer status;

    /**
     * 操作类型(0创建 1调整 2通过 3挂起)
     */
    @TableField("opt_type")
    private Integer optType;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 备注挂起原因等
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 计划项目目标集合
     */
    @TableField(exist = false)
    private List<AbaPlanProjectGoal> planProjectGoalList;

    @Serial
    private static final long serialVersionUID = 1L;
}
