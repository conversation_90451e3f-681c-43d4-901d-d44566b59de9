package com.songbai.qscz.project.qscz.aba.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.songbai.qscz.project.qscz.aba.dto.AbaPlanProjectDto;
import com.songbai.qscz.project.qscz.aba.model.AbaPlanProject;

import java.util.List;


public interface AbaPlanProjectService extends IService<AbaPlanProject> {

    /**
     * 根据计划ID获取计划项目列表
     * @param planId
     * @return
     */
    List<AbaPlanProjectDto> getListByPlanId(Integer planId);
}
