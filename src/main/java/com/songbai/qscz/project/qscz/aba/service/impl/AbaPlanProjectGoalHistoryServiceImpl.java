package com.songbai.qscz.project.qscz.aba.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.songbai.qscz.project.qscz.aba.dto.AbaPlanProjectGoalHistoryDto;
import com.songbai.qscz.project.qscz.aba.mapper.AbaPlanProjectGoalHistoryMapper;
import com.songbai.qscz.project.qscz.aba.model.AbaPlanProjectGoalHistory;
import com.songbai.qscz.project.qscz.aba.service.AbaPlanProjectGoalHistoryService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 计划项目目标历史表(AbaPlanProjectGoalHistory)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-28 15:44:06
 */
@Service ("abaPlanProjectGoalHistoryService")
public class AbaPlanProjectGoalHistoryServiceImpl extends ServiceImpl<AbaPlanProjectGoalHistoryMapper, AbaPlanProjectGoalHistory> implements AbaPlanProjectGoalHistoryService {

    @Resource
    private AbaPlanProjectGoalHistoryMapper abaPlanProjectGoalHistoryMapper;

    /**
     * 获取短期目标调整历史
     *
     * @param planProjectGoalId 短期目标ID
     * @return
     */
    @Override
    public List<AbaPlanProjectGoalHistoryDto> getAdjustHistoryList (Integer planProjectGoalId) {
        LambdaQueryWrapper<AbaPlanProjectGoalHistory> queryWrapper = new LambdaQueryWrapper<AbaPlanProjectGoalHistory> ()
            .eq (AbaPlanProjectGoalHistory::getPlanProjectGoalId, planProjectGoalId)
            .orderByAsc (AbaPlanProjectGoalHistory::getCreateTime);
        List<AbaPlanProjectGoalHistory> goalHistoryList = abaPlanProjectGoalHistoryMapper.selectList (queryWrapper);
        return BeanUtil.copyToList (goalHistoryList, AbaPlanProjectGoalHistoryDto.class);
    }
}
