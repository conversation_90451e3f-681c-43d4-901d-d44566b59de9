package com.songbai.qscz.project.qscz.aba.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.songbai.qscz.project.qscz.aba.dto.AbaDailyDataDto;
import com.songbai.qscz.project.qscz.aba.model.AbaDailyGoalData;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AbaDailyGoalDataService extends IService<AbaDailyGoalData> {

    /**
     * 获取短期目标的训练数据
     * @param planId 计划ID
     * @param planProjectGoalId 计划短期目标ID
     * @return
     */
    List<AbaDailyDataDto> getDailyDataList (Integer planId, Integer planProjectGoalId);

}
