package com.songbai.qscz.project.qscz.aba.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.songbai.qscz.project.qscz.aba.dto.YcxChildrenDto;
import com.songbai.qscz.project.qscz.aba.mapper.YcxChildrenMapper;
import com.songbai.qscz.project.qscz.aba.mapper.YcxChildrenSurveyMapper;
import com.songbai.qscz.project.qscz.aba.model.YcxChildren;
import com.songbai.qscz.project.qscz.aba.model.YcxChildrenSurvey;
import com.songbai.qscz.project.qscz.aba.service.YcxChildrenService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * (YcxChildren)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-27 10:12:48
 */
@Service ("ycxChildrenService")
public class YcxChildrenServiceImpl extends ServiceImpl<YcxChildrenMapper, YcxChildren> implements YcxChildrenService {

    @Resource
    private YcxChildrenMapper ycxChildrenMapper;
    @Resource
    private YcxChildrenSurveyMapper ycxChildrenSurveyMapper;

    /**
     * 获取儿童基本信息
     *
     * @param childId
     * @return
     */
    @Override
    public YcxChildrenDto getChildInfo (Integer childId) {
        LambdaQueryWrapper<YcxChildren> childQueryWrapper = new LambdaQueryWrapper<YcxChildren> ().eq (YcxChildren::getId, childId)
            .select (YcxChildren::getId, YcxChildren::getGender, YcxChildren::getBirthday, YcxChildren::getPregnantWeek);
        YcxChildren children = ycxChildrenMapper.selectOne (childQueryWrapper);

        LambdaQueryWrapper<YcxChildrenSurvey> surveyLambdaQueryWrapper = new LambdaQueryWrapper<YcxChildrenSurvey> ()
            .eq (YcxChildrenSurvey::getChildrenId, childId)
            .orderByDesc (YcxChildrenSurvey::getCreateTime);
        List<YcxChildrenSurvey> surveyList = ycxChildrenSurveyMapper.selectList (surveyLambdaQueryWrapper);


        YcxChildrenDto childrenDto = new YcxChildrenDto ();
        childrenDto.setId (children.getId ());
        childrenDto.setGender (Objects.isNull (children.getGender ())?"未知":(children.getGender () == 1 ? "男" : "女"));
        childrenDto.setAgeInMonth (DateUtil.betweenMonth(children.getBirthday (), new Date (), false));
        childrenDto.setPregnantWeek (children.getPregnantWeek ());
        if(CollectionUtil.isNotEmpty (surveyList)){
            YcxChildrenSurvey survey = surveyList.get (0);
            childrenDto.setFamilyMedicalHistory (survey.getFamilyMedicalHistory ());
            childrenDto.setComorbidCondition (survey.getComorbidCondition ());
            childrenDto.setIsSchool (survey.getIsSchool ());
            childrenDto.setRehabilitationHistory (survey.getRehabilitationHistory ());
            childrenDto.setRehabilitationGoalByParents (survey.getRehabilitationGoal ());
        }
        return childrenDto;
    }
}
