package com.songbai.qscz.project.qscz.aba.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.songbai.qscz.project.qscz.aba.dto.AbaPlanDomainDto;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@TableName("aba_plan")
public class AbaPlan implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 机构ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 儿童ID
     */
    @TableField("child_id")
    private Integer childId;

    /**
     * 每个项目最小回合数
     */
    @TableField("project_min_round")
    private Integer projectMinRound;

    /**
     * 状态(0已关闭 1进行中)
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 计划领域集合
     */
    @TableField(exist = false)
    private List<AbaPlanDomainDto> planDomainList;

    @Serial
    private static final long serialVersionUID = 1L;
}
