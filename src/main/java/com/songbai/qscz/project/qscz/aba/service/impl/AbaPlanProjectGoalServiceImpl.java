package com.songbai.qscz.project.qscz.aba.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.songbai.qscz.project.qscz.aba.mapper.AbaPlanProjectGoalMapper;
import com.songbai.qscz.project.qscz.aba.model.AbaPlanProjectGoal;
import com.songbai.qscz.project.qscz.aba.service.AbaPlanProjectGoalService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 计划项目目标表(AbaPlanProjectGoal)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-26 14:33:38
 */
@Service ("abaPlanProjectGoalService")
public class AbaPlanProjectGoalServiceImpl extends ServiceImpl<AbaPlanProjectGoalMapper, AbaPlanProjectGoal> implements AbaPlanProjectGoalService {

    @Resource
    private AbaPlanProjectGoalMapper abaPlanProjectGoalMapper;

    /**
     * 根据计划项目ID集合获取短期目标列表
     *
     * @param planProjectIds 计划项目ID集合
     * @return
     */
    @Override
    public List<AbaPlanProjectGoal> getProjectGoalListByProjectIds (List<Integer> planProjectIds) {
        LambdaQueryWrapper<AbaPlanProjectGoal> queryWrapper = new LambdaQueryWrapper<AbaPlanProjectGoal> ()
            .in (AbaPlanProjectGoal::getPlanProjectId, planProjectIds);
        return abaPlanProjectGoalMapper.selectList (queryWrapper);
    }
}
