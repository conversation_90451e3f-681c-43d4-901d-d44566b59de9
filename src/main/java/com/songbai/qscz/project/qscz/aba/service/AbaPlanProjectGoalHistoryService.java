package com.songbai.qscz.project.qscz.aba.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.songbai.qscz.project.qscz.aba.dto.AbaPlanProjectGoalHistoryDto;
import com.songbai.qscz.project.qscz.aba.model.AbaPlanProjectGoalHistory;

import java.util.List;

/**
 * 计划项目目标历史表(AbaPlanProjectGoalHistory)表服务接口
 *
 * <AUTHOR>
 * @since 2025-08-28 15:44:06
 */
public interface AbaPlanProjectGoalHistoryService extends IService<AbaPlanProjectGoalHistory> {

    /**
     * 获取短期目标调整历史
     * @param planProjectGoalId 短期目标ID
     * @return
     */
    List<AbaPlanProjectGoalHistoryDto> getAdjustHistoryList(Integer planProjectGoalId);
}
