package com.songbai.qscz.project.qscz.aba.model;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * ABA-课程备注(AbaCourseRemark)表实体类
 *
 * <AUTHOR>
 * @since 2025-08-27 16:16:39
 */
@Data
@Accessors (chain = true)
@TableName ("aba_course_remark")
public class AbaCourseRemark {

    /**
     * 主键
     */
    @TableField ("id")
    private Integer id;

    /**
     * 日期
     */
    @TableField ("date")
    private Date date;

    /**
     * 机构ID
     */
    @TableField ("org_id")
    private Integer orgId;

    /**
     * 儿童ID
     */
    @TableField ("child_id")
    private Integer childId;

    /**
     * 计划ID
     */
    @TableField ("plan_id")
    private Integer planId;

    /**
     * 课程ID
     */
    @TableField ("course_id")
    private Integer courseId;

    /**
     * 老师ID
     */
    @TableField ("teacher_id")
    private Integer teacherId;

    /**
     * 是否按时（0否，1是）
     */
    @TableField ("is_ontime")
    private Integer isOntime;

    /**
     * 未按时完成原因
     */
    @TableField ("nontime_reason")
    private String nontimeReason;

    /**
     * 孩子配合情况
     */
    @TableField ("child_match")
    private Integer childMatch;

    /**
     * 完成情况（0未完成，1完成）
     */
    @TableField ("is_complete")
    private Integer isComplete;

    /**
     * 未完成原因
     */
    @TableField ("uncomplete_reason")
    private String uncompleteReason;

    /**
     * 备注
     */
    @TableField ("remark")
    private String remark;

    /**
     * 视频
     */
    @TableField ("video_url")
    private String videoUrl;

    /**
     * 督导ID
     */
    @TableField ("supervisor_id")
    private Integer supervisorId;

    /**
     * 督导评价
     */
    @TableField ("supervisor_evaluation")
    private String supervisorEvaluation;

    /**
     * 督导评价时间
     */
    @TableField ("evaluation_time")
    private Date evaluationTime;

    /**
     * 创建人
     */
    @TableField ("create_user")
    private String createUser;

    /**
     * 更新人
     */
    @TableField ("update_user")
    private String updateUser;

    /**
     * 是否已读(0未读 1已读)
     */
    @TableField ("is_read")
    private Integer isRead;

}
