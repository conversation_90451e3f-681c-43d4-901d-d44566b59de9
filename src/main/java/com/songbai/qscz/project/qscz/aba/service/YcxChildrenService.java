package com.songbai.qscz.project.qscz.aba.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.songbai.qscz.project.qscz.aba.dto.YcxChildrenDto;
import com.songbai.qscz.project.qscz.aba.model.YcxChildren;

/**
 * (YcxChildren)表服务接口
 *
 * <AUTHOR>
 * @since 2025-08-27 10:12:48
 */
public interface YcxChildrenService extends IService<YcxChildren> {

    /**
     * 获取儿童基本信息
     * @param childId
     * @return
     */
    YcxChildrenDto getChildInfo(Integer childId);
}
