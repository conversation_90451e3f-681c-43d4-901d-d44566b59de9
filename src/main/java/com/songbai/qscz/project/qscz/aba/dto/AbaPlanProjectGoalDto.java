package com.songbai.qscz.project.qscz.aba.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 计划项目目标表(AbaPlanProjectGoal)表实体类
 *
 * <AUTHOR>
 * @since 2025-08-26 14:33:38
 */
@Data
public class AbaPlanProjectGoalDto {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 计划ID
     */
    private Integer planId;

    /**
     * 领域ID
     */
    private Integer domainId;

    /**
     * 计划项目ID
     */
    private Integer planProjectId;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 短期目标ID
     */
    private Integer shortGoalId;

    /**
     * 短期目标名称
     */
    private String shortGoalName;

    /**
     * 辅助方式
     */
    private String assistMethod;

    /**
     * 目标(Json格式存储)
     */
    private String target;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 记录方式（1回合制 2试探 3分步骤 ）
     */
    private Integer recordType;

    /**
     * 状态((1进行中 2通过 3挂起)
     */
    private Integer status;

    /**
     * 挂起原因
     */
    private String remarks;

}
