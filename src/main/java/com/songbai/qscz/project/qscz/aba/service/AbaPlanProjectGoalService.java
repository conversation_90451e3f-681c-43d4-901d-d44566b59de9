package com.songbai.qscz.project.qscz.aba.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.songbai.qscz.project.qscz.aba.model.AbaPlanProjectGoal;

import java.util.List;

/**
 * 计划项目目标表(AbaPlanProjectGoal)表服务接口
 *
 * <AUTHOR>
 * @since 2025-08-26 14:33:38
 */
public interface AbaPlanProjectGoalService extends IService<AbaPlanProjectGoal> {

    /**
     * 根据计划项目ID集合获取短期目标列表
     * @param planProjectIds 计划项目ID集合
     * @return
     */
    List<AbaPlanProjectGoal> getProjectGoalListByProjectIds(List<Integer> planProjectIds);

}
