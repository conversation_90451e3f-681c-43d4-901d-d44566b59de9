package com.songbai.qscz.project.qscz.aba.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("aba_project")
public class AbaProject implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 领域ID
     */
    @TableField("domain_id")
    private Integer domainId;

    /**
     * 领域
     */
    @TableField("domain_name")
    private String domainName;

    /**
     * 项目
     */
    @TableField("name")
    private String name;

    /**
     * 阶段
     */
    @TableField("level")
    private Integer level;

    /**
     * 长期目标
     */
    @TableField("long_goal")
    private String longGoal;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    @Serial
    private static final long serialVersionUID = 1L;
}
