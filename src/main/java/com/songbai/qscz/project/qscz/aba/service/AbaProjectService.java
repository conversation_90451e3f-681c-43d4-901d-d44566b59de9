package com.songbai.qscz.project.qscz.aba.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.songbai.qscz.project.qscz.aba.dto.AbaProjectDto;
import com.songbai.qscz.project.qscz.aba.model.AbaProject;

/**
 * (AbaProject)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-27 10:22:32
 */
public interface AbaProjectService extends IService<AbaProject> {

    /**
     * 获取项目详细信息
     * @param projectId 项目ID
     * @return
     */
    AbaProjectDto getProjectDetail(Integer projectId);
}
