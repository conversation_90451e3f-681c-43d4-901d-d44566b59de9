package com.songbai.qscz.project.qscz.aba.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AbaDailyDataDto implements Serializable {

    /**
     * 日期
     */
    private Date date;

    /**
     * 儿童ID
     */
    private Integer childId;

    /**
     * 计划ID
     */
    private Integer planId;

    /**
     * 计划项目ID
     */
    private Integer planProjectId;

    /**
     * 计划项目目标ID
     */
    private Integer planProjectGoalId;

    /**
     * 短期目标ID
     */
    private Integer shortGoalId;

    /**
     * 短期目标名称
     */
    private String shortGoalName;

    /**
     * 辅助方式
     */
    private String assistMethod;

    /**
     * 小目标结果
     */
    private String targetResult;

    /**
     * 记录方式（1回合制 2试探 3分步骤 ）
     */
    private Integer recordType;


    /**
     * 领域ID
     */
    private Integer domainId;

    /**
     * 领域名称
     */
    private String domainName;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目阶段
     */
    private Integer projectLevel;

    /**
     * 通过率
     */
    private Double passRate;

    /**
     * 回合制是否达标(3天连续达到80%（含），或2天连续达到90%（含），或1天100%)
     */
    private Integer isContinuousPass;

    /**
     * 回合制是否异常(3天连续在50%（含）以下，或2天连续在30%（含）以下)
     */
    private Integer isFail;

    /**
     * 回合制是否缓慢(连续5天，在51%（含）-79%（含）之间忽上忽下)
     */
    private Integer isSlow;

    /**
     * 每日课程备注（备注中可能存在对其他康复项目的描述）
     */
    private List<AbaCourseRemarkDto> courseRemarkList;

    /**
     * 每日课程训练数据（只查询记录了备注的回合数据）
     */
    private List<AbaCourseRecordRemarkDto> courseRecordRemarkList;


    @Serial
    private static final long serialVersionUID = 1L;
}
