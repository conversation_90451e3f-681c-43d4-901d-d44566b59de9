package com.songbai.qscz.project.qscz.aba.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.songbai.qscz.project.qscz.aba.dto.AbaCourseRemarkDto;
import com.songbai.qscz.project.qscz.aba.dto.AbaDailyDataDto;
import com.songbai.qscz.project.qscz.aba.mapper.AbaCourseRecordMapper;
import com.songbai.qscz.project.qscz.aba.mapper.AbaCourseRemarkMapper;
import com.songbai.qscz.project.qscz.aba.mapper.AbaDailyGoalDataMapper;
import com.songbai.qscz.project.qscz.aba.model.AbaCourseRemark;
import com.songbai.qscz.project.qscz.aba.model.AbaDailyGoalData;
import com.songbai.qscz.project.qscz.aba.service.AbaDailyGoalDataService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
public class AbaDailyGoalDataServiceImpl extends ServiceImpl<AbaDailyGoalDataMapper, AbaDailyGoalData> implements AbaDailyGoalDataService {

    @Resource
    private AbaDailyGoalDataMapper abaDailyGoalDataMapper;
    @Resource
    private AbaCourseRemarkMapper abaCourseRemarkMapper;
    @Resource
    private AbaCourseRecordMapper abaCourseRecordMapper;

    /**
     * 获取短期目标的训练数据
     *
     * @param planId         计划ID
     * @param planProjectGoalId 短期目标ID
     * @return
     */
    @Override
    public List<AbaDailyDataDto> getDailyDataList (Integer planId, Integer planProjectGoalId) {
        // 每日数据
        LambdaQueryWrapper<AbaDailyGoalData> dailyGoalDataQueryWrapper = new LambdaQueryWrapper<AbaDailyGoalData> ()
            .eq (AbaDailyGoalData::getPlanId, planId)
            .eq (AbaDailyGoalData::getPlanProjectGoalId, planProjectGoalId)
            .orderByAsc (AbaDailyGoalData::getDate);
        List<AbaDailyGoalData> dailyGoalDataList = abaDailyGoalDataMapper.selectList (dailyGoalDataQueryWrapper);
        if(CollectionUtil.isEmpty (dailyGoalDataList)){
            return List.of ();
        }
        List<AbaDailyDataDto> dailyDataDtoList = BeanUtil.copyToList (dailyGoalDataList, AbaDailyDataDto.class);

        Date startTime = DateUtil.beginOfDay (dailyDataDtoList.getFirst ().getDate ());
        Date endTime = DateUtil.endOfDay (dailyDataDtoList.getLast ().getDate ());

        // 每日课程备注数据
        LambdaQueryWrapper<AbaCourseRemark> courseRemarkQueryWrapper = new LambdaQueryWrapper<AbaCourseRemark> ()
            .eq (AbaCourseRemark::getPlanId, planId)
            .ge (AbaCourseRemark::getDate, startTime)
            .le (AbaCourseRemark::getDate, endTime);
        List<AbaCourseRemark> courseRemarkList = abaCourseRemarkMapper.selectList (courseRemarkQueryWrapper);
        Map<String, List<AbaCourseRemarkDto>> courseRemarkMap = BeanUtil.copyToList (courseRemarkList, AbaCourseRemarkDto.class).stream ()
            .collect (Collectors.groupingBy (t -> DateUtil.formatDate (t.getDate())));

        // 每日课程回合记录备注
        // TODO 目前库中记录的备注数据与设想的不太一样，主要是对小目标的补充描述和辅助方式，暂时可能意义不大
        // LambdaQueryWrapper<AbaCourseRecord> courseRecordQueryWrapper = new LambdaQueryWrapper<AbaCourseRecord> ()
        //     .eq (AbaCourseRecord::getPlanProjectGoalId, planProjectGoalId)
        //     .in (AbaCourseRecord::getCourseId, courseRemarkList.stream ().map (AbaCourseRemark::getCourseId).collect (Collectors.toList ()))
        //     .isNull (AbaCourseRecord::getRemark);
        // List<AbaCourseRecord> courseRecordList = abaCourseRecordMapper.selectList (courseRecordQueryWrapper);
        // Map<Integer, List<AbaCourseRecordRemarkDto>> courseRecordMap = BeanUtil.copyToList (courseRecordList, AbaCourseRecordRemarkDto.class).stream ()
        //     .collect (Collectors.groupingBy (t -> DateUtil.formatDate (t.get));
        dailyDataDtoList.forEach (data -> {
            data.setCourseRemarkList (courseRemarkMap.get (DateUtil.formatDate (data.getDate ())));
        });
        return dailyDataDtoList;
    }
}
