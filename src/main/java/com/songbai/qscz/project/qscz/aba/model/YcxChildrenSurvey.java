package com.songbai.qscz.project.qscz.aba.model;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * (YcxChildrenSurvey)表实体类
 *
 * <AUTHOR>
 * @since 2025-08-27 10:13:11
 */
@Data
@Accessors (chain = true)
@TableName ("ycx_children_survey")
public class YcxChildrenSurvey {

    @TableField ("id")
    private Integer id;

    /**
     * 儿童ID
     */
    @TableField ("children_id")
    private Integer childrenId;

    /**
     * 常住地
     */
    @TableField ("residence")
    private String residence;

    /**
     * 主要照顾者
     */
    @TableField ("primary_caregiver")
    private String primaryCaregiver;

    /**
     * 父亲信息-孩子出生时年龄
     */
    @TableField ("father_birth_age")
    private Integer fatherBirthAge;

    /**
     * 父亲信息-常住地
     */
    @TableField ("father_residence")
    private String fatherResidence;

    /**
     * 父亲信息-职业
     */
    @TableField ("father_profession")
    private String fatherProfession;

    /**
     * 母亲信息-孩子出生时年龄
     */
    @TableField ("mother_birth_age")
    private Integer motherBirthAge;

    /**
     * 母亲信息-职业
     */
    @TableField ("mother_profession")
    private String motherProfession;

    /**
     * 母亲信息-常住地
     */
    @TableField ("mother_residence")
    private String motherResidence;

    /**
     * 家族病史
     */
    @TableField ("family_medical_history")
    private String familyMedicalHistory;

    /**
     * 出生情况
     */
    @TableField ("birth_condition")
    private String birthCondition;

    /**
     * 发育史
     */
    @TableField ("growth_history")
    private String growthHistory;

    /**
     * 视力是否正常
     */
    @TableField ("vision")
    private String vision;

    /**
     * 听力是否正常
     */
    @TableField ("hearing")
    private String hearing;

    /**
     * 医学诊断
     */
    @TableField ("medical_diagnosis")
    private String medicalDiagnosis;

    /**
     * 共患病情况
     */
    @TableField ("comorbid_condition")
    private String comorbidCondition;

    /**
     * 严重受伤、住院或手术
     */
    @TableField ("serious_injury")
    private String seriousInjury;

    /**
     * 是否在上学
     */
    @TableField ("is_school")
    private String isSchool;

    /**
     * 强化物-食物类
     */
    @TableField ("reinforcement_food")
    private String reinforcementFood;

    /**
     * 强化物-玩具类
     */
    @TableField ("reinforcement_toy")
    private String reinforcementToy;

    /**
     * 强化物-活动类
     */
    @TableField ("reinforcement_activity")
    private String reinforcementActivity;

    /**
     * 强化物-其他类
     */
    @TableField ("reinforcement_other")
    private String reinforcementOther;

    /**
     * 抗拒或害怕的事情/东西
     */
    @TableField ("resistance_fear")
    private String resistanceFear;

    /**
     * 康复治疗历史
     */
    @TableField ("rehabilitation_history")
    private String rehabilitationHistory;

    /**
     * 康复目标
     */
    @TableField ("rehabilitation_goal")
    private String rehabilitationGoal;

    /**
     * 填写来源(1医生端, 2家长端)
     */
    @TableField ("source")
    private Integer source;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

}
