package com.songbai.qscz.project.qscz.aba.model;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * (AbaCourseRecord)表实体类
 *
 * <AUTHOR>
 * @since 2025-08-27 15:58:48
 */
@Data
@Accessors (chain = true)
@TableName ("aba_course_record")
public class AbaCourseRecord {

    /**
     * 主键
     */
    @TableField ("id")
    private Integer id;

    /**
     * 课程ID
     */
    @TableField ("course_id")
    private Integer courseId;

    /**
     * 课程项目ID
     */
    @TableField ("course_project_id")
    private Integer courseProjectId;

    /**
     * 项目ID
     */
    @TableField ("project_id")
    private Integer projectId;

    /**
     * 目标名称
     */
    @TableField ("target_name")
    private String targetName;

    /**
     * 目标颜色色值
     */
    @TableField ("target_color")
    private String targetColor;

    /**
     * 目标达成状态(0未达成 1已达成)
     */
    @TableField ("target_done")
    private Integer targetDone;

    /**
     * 辅助方式(1FP 2PP 3GP 4VP 5MP 6RF 7-PP)
     */
    @TableField ("assist_method")
    private Integer assistMethod;

    /**
     * 备注
     */
    @TableField ("remark")
    private String remark;

    /**
     * 是否有效(0无效 1有效)
     */
    @TableField ("status")
    private Integer status;

    /**
     * 创建人
     */
    @TableField ("create_user")
    private String createUser;

    /**
     * 更新人
     */
    @TableField ("update_user")
    private String updateUser;

    /**
     * 课程项目目标ID
     */
    @TableField ("course_project_goal_id")
    private Integer courseProjectGoalId;

    /**
     * 短期目标ID
     */
    @TableField ("short_goal_id")
    private Integer shortGoalId;

    /**
     * 计划项目目标ID
     */
    @TableField ("plan_project_goal_id")
    private Integer planProjectGoalId;

}
