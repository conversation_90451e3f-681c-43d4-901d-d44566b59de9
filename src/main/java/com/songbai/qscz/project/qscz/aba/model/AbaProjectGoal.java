package com.songbai.qscz.project.qscz.aba.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("aba_project_goal")
public class AbaProjectGoal implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 短期目标
     */
    @TableField("short_goal")
    private String shortGoal;

    /**
     * 操作方法
     */
    @TableField("operate_method")
    private String operateMethod;

    /**
     * 操作示范
     */
    @TableField("operate_video")
    private String operateVideo;

    /**
     * 注意事项
     */
    @TableField("notice")
    private String notice;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 辅助方式
     */
    @TableField("assist_method")
    private String assistMethod;


    @Serial
    private static final long serialVersionUID = 1L;
}
