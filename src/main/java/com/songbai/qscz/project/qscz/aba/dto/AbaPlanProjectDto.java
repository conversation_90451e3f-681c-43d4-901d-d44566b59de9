package com.songbai.qscz.project.qscz.aba.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AbaPlanProjectDto implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 计划ID
     */
    private Integer planId;

    /**
     * 领域ID
     */
    private Integer domainId;

    /**
     * 领域名称
     */
    private String domainName;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目阶段
     */
    private Integer projectLevel;

    /**
     * 长期目标
     */
    private String longGoal;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 状态(1进行中 2通过 3挂起)
     */
    private Integer status;

    /**
     * 备注挂起原因等
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 计划项目目标集合
     */
    private List<AbaPlanProjectGoalDto> planProjectGoalList;

    @Serial
    private static final long serialVersionUID = 1L;
}
