package com.songbai.qscz.project.qscz.aba.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.songbai.qscz.project.qscz.aba.dto.AbaPlanProjectDto;
import com.songbai.qscz.project.qscz.aba.dto.AbaPlanProjectGoalDto;
import com.songbai.qscz.project.qscz.aba.mapper.AbaPlanProjectMapper;
import com.songbai.qscz.project.qscz.aba.model.AbaPlanProject;
import com.songbai.qscz.project.qscz.aba.model.AbaPlanProjectGoal;
import com.songbai.qscz.project.qscz.aba.service.AbaPlanProjectGoalService;
import com.songbai.qscz.project.qscz.aba.service.AbaPlanProjectService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class AbaPlanProjectServiceImpl extends ServiceImpl<AbaPlanProjectMapper, AbaPlanProject> implements AbaPlanProjectService {

    @Resource
    private AbaPlanProjectMapper planProjectMapper;
    @Resource
    private AbaPlanProjectGoalService planProjectGoalService;


    /**
     * 根据计划ID获取计划项目列表
     *
     * @param planId
     * @return
     */
    @Override
    public List<AbaPlanProjectDto> getListByPlanId (Integer planId) {
        // 查询项目集合
        LambdaQueryWrapper<AbaPlanProject> queryWrapper = new LambdaQueryWrapper<AbaPlanProject> ()
            .eq (AbaPlanProject::getPlanId, planId)
            .orderByAsc (AbaPlanProject::getDomainId)
            .orderByAsc (AbaPlanProject::getStatus);
        List<AbaPlanProject> planProjectList = planProjectMapper.selectList (queryWrapper);
        List<AbaPlanProjectDto> abaPlanProjectDtolist = new ArrayList<> ();
        if (CollectionUtil.isNotEmpty (planProjectList)) {
            List<Integer> planProjectIds = planProjectList.stream ().map (AbaPlanProject::getId).collect (Collectors.toList ());
            List<AbaPlanProjectGoal> projectGoalList = planProjectGoalService.getProjectGoalListByProjectIds (planProjectIds);
            abaPlanProjectDtolist = planProjectList.stream ()
                .map (project -> {
                    AbaPlanProjectDto dto = BeanUtil.copyProperties (project, AbaPlanProjectDto.class);
                    List<AbaPlanProjectGoalDto> goalList = projectGoalList.stream ()
                        .filter (goal -> goal.getPlanProjectId ().equals (project.getId ()))
                        .map (t -> BeanUtil.copyProperties (t, AbaPlanProjectGoalDto.class))
                        .collect (Collectors.toList ());
                    dto.setPlanProjectGoalList (goalList);
                    return dto;
                }).collect (Collectors.toList ());
        }
        return abaPlanProjectDtolist;
    }
}
