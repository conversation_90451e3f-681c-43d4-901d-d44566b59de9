package com.songbai.qscz.project.qscz.aba.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.songbai.qscz.project.qscz.aba.dto.AbaPlanDomainDto;
import com.songbai.qscz.project.qscz.aba.dto.AbaPlanDto;
import com.songbai.qscz.project.qscz.aba.dto.AbaPlanProjectDto;
import com.songbai.qscz.project.qscz.aba.mapper.AbaPlanMapper;
import com.songbai.qscz.project.qscz.aba.model.AbaPlan;
import com.songbai.qscz.project.qscz.aba.service.AbaPlanProjectService;
import com.songbai.qscz.project.qscz.aba.service.AbaPlanService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
public class AbaPlanServiceImpl extends ServiceImpl<AbaPlanMapper, AbaPlan> implements AbaPlanService {

    @Resource
    private AbaPlanMapper planMapper;
    @Resource
    private AbaPlanProjectService planProjectService;


    /**
     * 根据儿童ID获取计划详情(包含已通过和已挂起的项目)
     *
     * @param planId 计划ID
     * @return
     */
    @Override
    public AbaPlanDto getPlanDetailByPlanId (Integer planId) {
        AbaPlan plan = this.getById (planId);
        AbaPlanDto planDto = BeanUtil.copyProperties (plan, AbaPlanDto.class);
        List<AbaPlanProjectDto> planProjectList = planProjectService.getListByPlanId (planId);
        planDto.setPlanDomainList (this.buildPlanDomainList (planProjectList));
        return planDto;
    }

    /**
     * 构建领域集合
     *
     * @param planProjectList 计划项目集合
     * @return
     */
    private List<AbaPlanDomainDto> buildPlanDomainList (List<AbaPlanProjectDto> planProjectList) {
        List<AbaPlanDomainDto> domainVoList = new ArrayList<> ();
        Map<Integer, List<AbaPlanProjectDto>> domainMap = planProjectList.stream ()
            .collect (Collectors.groupingBy (AbaPlanProjectDto::getDomainId, LinkedHashMap::new, Collectors.toList ()));
        domainMap.forEach ((key, value) -> {
            AbaPlanDomainDto domainVo = new AbaPlanDomainDto ();
            domainVo.setId (key);
            domainVo.setDomainName (value.get (0).getDomainName ());
            domainVo.setPlanProjectList (value);
            domainVoList.add (domainVo);
        });
        return domainVoList;
    }

}
