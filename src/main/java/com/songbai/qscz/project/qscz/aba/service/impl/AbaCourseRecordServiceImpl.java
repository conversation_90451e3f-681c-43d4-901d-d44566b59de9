package com.songbai.qscz.project.qscz.aba.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.songbai.qscz.project.qscz.aba.mapper.AbaCourseRecordMapper;
import com.songbai.qscz.project.qscz.aba.model.AbaCourseRecord;
import com.songbai.qscz.project.qscz.aba.service.AbaCourseRecordService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * (AbaCourseRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-27 15:58:48
 */
@Service ("abaCourseRecordService")
public class AbaCourseRecordServiceImpl extends ServiceImpl<AbaCourseRecordMapper, AbaCourseRecord> implements AbaCourseRecordService {

    @Resource
    private AbaCourseRecordMapper abaCourseRecordMapper;
}
