package com.songbai.qscz.project.qscz.aba.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AbaProjectDto implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 领域ID
     */
    private Integer domainId;

    /**
     * 领域
     */
    private String domainName;

    /**
     * 项目
     */
    private String name;

    /**
     * 阶段
     */
    private Integer level;

    /**
     * 长期目标
     */
    private String longGoal;

    /**
     * 短期目标集合
     */
    List<AbaProjectGoalDto> projectGoalList;

    @Serial
    private static final long serialVersionUID = 1L;
}
