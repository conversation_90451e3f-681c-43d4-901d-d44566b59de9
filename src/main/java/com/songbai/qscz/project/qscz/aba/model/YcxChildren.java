package com.songbai.qscz.project.qscz.aba.model;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * (YcxChildren)表实体类
 *
 * <AUTHOR>
 * @since 2025-08-27 10:12:49
 */
@Data
@Accessors (chain = true)
@TableName ("ycx_children")
public class YcxChildren {

    /**
     * 主键
     */
    @TableField ("id")
    private Integer id;

    /**
     * ERP患者ID
     */
    @TableField ("erp_patient_id")
    private Integer erpPatientId;

    /**
     * 身份证号
     */
    @TableField ("id_card")
    private String idCard;

    /**
     * 儿童编号
     */
    @TableField ("child_no")
    private String childNo;

    /**
     * 姓名
     */
    @TableField ("name")
    private String name;

    /**
     * 监护人
     */
    @TableField ("guardian")
    private String guardian;

    /**
     * 头像
     */
    @TableField ("logo")
    private String logo;

    /**
     * 性别1男2女
     */
    @TableField ("gender")
    private Integer gender;

    /**
     * 生日
     */
    @TableField ("birthday")
    private Date birthday;

    /**
     * 民族
     */
    @TableField ("nation")
    private String nation;

    /**
     * 是否城镇(0否，1是)
     */
    @TableField ("is_city")
    private Integer isCity;

    /**
     * 联系电话
     */
    @TableField ("link_mobile")
    private String linkMobile;

    /**
     * 地址
     */
    @TableField ("address")
    private String address;

    /**
     * 孕周
     */
    @TableField ("pregnant_week")
    private String pregnantWeek;

    /**
     * 省
     */
    @TableField ("province")
    private Integer province;

    /**
     * 市
     */
    @TableField ("city")
    private Integer city;

    /**
     * 区
     */
    @TableField ("area")
    private Integer area;

    /**
     * 单位ID
     */
    @TableField ("org_id")
    private Integer orgId;

    /**
     * 是否请假(0未请假 1已请假)
     */
    @TableField ("is_leave")
    private Integer isLeave;

    /**
     * 是否结课(0未结课 1已结课)
     */
    @TableField ("is_course_completed")
    private Integer isCourseCompleted;

    /**
     * 创建人id
     */
    @TableField ("creator")
    private Integer creator;

    /**
     * 创建人姓名
     */
    @TableField ("create_name")
    private String createName;

    /**
     * 更新人
     */
    @TableField ("update_user")
    private String updateUser;

    /**
     * 家长id
     */
    @TableField ("parent_id")
    private Integer parentId;

}
