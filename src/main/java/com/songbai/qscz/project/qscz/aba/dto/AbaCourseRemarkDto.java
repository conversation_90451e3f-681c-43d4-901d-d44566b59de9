package com.songbai.qscz.project.qscz.aba.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * ABA-课程备注(AbaCourseRemark)表实体类
 *
 * <AUTHOR>
 * @since 2025-08-27 16:16:39
 */
@Data
public class AbaCourseRemarkDto {


    /**
     * 日期
     */
    private Date date;

    /**
     * 课程ID
     */
    private Integer courseId;

    /**
     * 是否按时（0否，1是）
     */
    private Integer isOntime;

    /**
     * 未按时完成原因
     */
    private String nontimeReason;

    /**
     * 孩子配合情况
     */
    private Integer childMatch;

    /**
     * 完成情况（0未完成，1完成）
     */
    private Integer isComplete;

    /**
     * 未完成原因
     */
    private String uncompleteReason;

    /**
     * 备注
     */
    private String remark;

    /**
     * 视频
     */
    // private String videoUrl;


    /**
     * 督导评价
     */
    private String supervisorEvaluation;


}
