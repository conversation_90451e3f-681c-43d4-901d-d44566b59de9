package com.songbai.qscz.common.web.util;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.v7.json.JSONUtil;
import com.songbai.qscz.common.web.filter.RepeatedlyRequestWrapper;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;

import java.io.BufferedReader;
import java.util.Map;

/**
 * 请求参数提取器
 * 用于从HttpServletRequest中提取请求参数，支持JSON、URL参数、表单参数等多种格式。
 *
 * <AUTHOR>
 */
@Slf4j
public class RequestParamExtractor {

    private static final int MAX_PARAM_LOG_LENGTH = 1000;

    /**
     * 提取请求参数
     * @param request
     * @return
     */
    public static String extractParameters(HttpServletRequest request) {
        try {
            StringBuilder logBuilder = new StringBuilder();

            String queryParam = getQueryParameters(request);
            boolean hasQueryParam = StringUtils.isNotEmpty(queryParam);

            if (isJsonRequest(request)) {
                String jsonParam = getJsonParameters(request);
                if (StringUtils.isNotEmpty(jsonParam)) {
                    logBuilder.append("参数类型[json]");
                    if (hasQueryParam) {
                        logBuilder.append(", URL参数: [").append(queryParam).append("]");
                    }
                    logBuilder.append(", JSON参数: [").append(truncateParam(jsonParam)).append("]");
                } else if (hasQueryParam) {
                    logBuilder.append("参数类型[query], 参数: [").append(queryParam).append("]");
                } else {
                    logBuilder.append("无参数");
                }
            } else {
                if (hasQueryParam) {
                    logBuilder.append("参数类型[query], 参数: [").append(queryParam).append("]");
                } else {
                    String formParam = getFormParameters(request);
                    if (StringUtils.isNotEmpty(formParam)) {
                        logBuilder.append("参数类型[form], 参数: [").append(truncateParam(formParam)).append("]");
                    } else {
                        logBuilder.append("无参数");
                    }
                }
            }

            return logBuilder.toString();
        } catch (Exception e) {
            return "请求参数解析失败: " + e.getMessage();
        }
    }

    /**
     * 获取JSON参数
     * @param request
     * @return
     */
    private static String getJsonParameters(HttpServletRequest request) {
        try {
            if (request instanceof RepeatedlyRequestWrapper) {
                return ((RepeatedlyRequestWrapper) request).getBodyString();
            } else {
                int contentLength = request.getContentLength();
                if (contentLength > 0 && contentLength < 10240) {
                    try (BufferedReader reader = request.getReader()) {
                        return IoUtil.read(reader);
                    }
                }
            }
        } catch (Exception e) {
            log.debug("读取JSON参数失败: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 获取URL参数
     * @param request
     * @return
     */
    private static String getQueryParameters(HttpServletRequest request) {
        try {
            String queryString = request.getQueryString();
            if (StringUtils.isNotEmpty(queryString) && queryString.length() <= MAX_PARAM_LOG_LENGTH) {
                return queryString;
            } else if (StringUtils.isNotEmpty(queryString)) {
                return queryString.substring(0, MAX_PARAM_LOG_LENGTH) + "...(参数过长已截断)";
            }
        } catch (Exception e) {
            log.debug("读取URL参数失败: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 获取表单参数
     * @param request
     * @return
     */
    private static String getFormParameters(HttpServletRequest request) {
        try {
            Map<String, String[]> parameterMap = request.getParameterMap();
            if (MapUtil.isNotEmpty(parameterMap)) {
                if (parameterMap.size() <= 20) {
                    return JSONUtil.toJsonStr(parameterMap);
                } else {
                    return "参数过多(" + parameterMap.size() + "个)，已省略";
                }
            }
        } catch (Exception e) {
            log.debug("读取表单参数失败: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 截断参数字符串，防止日志过长
     * @param param
     * @return
     */
    private static String truncateParam(String param) {
        if (StringUtils.isEmpty(param)) {
            return param;
        }
        if (param.length() > MAX_PARAM_LOG_LENGTH) {
            return param.substring(0, MAX_PARAM_LOG_LENGTH) + "...(参数过长已截断)";
        }
        return param;
    }

    /**
     * 判断是否为JSON请求
     * @param request
     * @return
     */
    private static boolean isJsonRequest(HttpServletRequest request) {
        String contentType = request.getContentType();
        if (contentType != null) {
            return StringUtils.startsWithIgnoreCase(contentType, MediaType.APPLICATION_JSON_VALUE);
        }

        String method = request.getMethod();
        return !"GET".equalsIgnoreCase(method) && !"DELETE".equalsIgnoreCase(method);
    }
}
