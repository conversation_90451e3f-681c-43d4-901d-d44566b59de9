<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.songbai.qscz.project.qscz.aba.mapper.AbaPlanProjectGoalHistoryMapper">
    <resultMap id="AbaPlanProjectGoalHistoryMap"
               type="com.songbai.qscz.project.qscz.aba.model.AbaPlanProjectGoalHistory">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orgId" column="org_id" jdbcType="INTEGER"/>
        <result property="childId" column="child_id" jdbcType="INTEGER"/>
        <result property="planId" column="plan_id" jdbcType="INTEGER"/>
        <result property="domainId" column="domain_id" jdbcType="INTEGER"/>
        <result property="planProjectId" column="plan_project_id" jdbcType="INTEGER"/>
        <result property="projectId" column="project_id" jdbcType="INTEGER"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        <result property="planProjectGoalId" column="plan_project_goal_id" jdbcType="INTEGER"/>
        <result property="shortGoalId" column="short_goal_id" jdbcType="INTEGER"/>
        <result property="shortGoalName" column="short_goal_name" jdbcType="VARCHAR"/>
        <result property="assistMethod" column="assist_method" jdbcType="VARCHAR"/>
        <result property="target" column="target" jdbcType="VARCHAR"/>
        <result property="recordType" column="record_type" jdbcType="INTEGER"/>
        <result property="optType" column="opt_type" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
        <result property="batchNo" column="batch_no" jdbcType="INTEGER"/>
        <result property="userType" column="user_type" jdbcType="INTEGER"/>
        <result property="isConfirm" column="is_confirm" jdbcType="INTEGER"/>
        <result property="confirmTime" column="confirm_time" jdbcType="TIMESTAMP"/>
        <result property="confirmUser" column="confirm_user" jdbcType="VARCHAR"/>
    </resultMap>
</mapper>

