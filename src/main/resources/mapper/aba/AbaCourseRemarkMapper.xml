<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.songbai.qscz.project.qscz.aba.mapper.AbaCourseRemarkMapper">
    <resultMap id="AbaCourseRemarkMap" type="com.songbai.qscz.project.qscz.aba.model.AbaCourseRemark">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="date" column="date" jdbcType="TIMESTAMP"/>
        <result property="orgId" column="org_id" jdbcType="INTEGER"/>
        <result property="childId" column="child_id" jdbcType="INTEGER"/>
        <result property="planId" column="plan_id" jdbcType="INTEGER"/>
        <result property="courseId" column="course_id" jdbcType="INTEGER"/>
        <result property="teacherId" column="teacher_id" jdbcType="INTEGER"/>
        <result property="isOntime" column="is_ontime" jdbcType="INTEGER"/>
        <result property="nontimeReason" column="nontime_reason" jdbcType="VARCHAR"/>
        <result property="childMatch" column="child_match" jdbcType="INTEGER"/>
        <result property="isComplete" column="is_complete" jdbcType="INTEGER"/>
        <result property="uncompleteReason" column="uncomplete_reason" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="videoUrl" column="video_url" jdbcType="VARCHAR"/>
        <result property="supervisorId" column="supervisor_id" jdbcType="INTEGER"/>
        <result property="supervisorEvaluation" column="supervisor_evaluation" jdbcType="VARCHAR"/>
        <result property="evaluationTime" column="evaluation_time" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="isRead" column="is_read" jdbcType="INTEGER"/>
    </resultMap>
</mapper>

