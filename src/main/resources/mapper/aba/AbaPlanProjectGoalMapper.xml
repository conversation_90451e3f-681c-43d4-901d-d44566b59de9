<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.songbai.qscz.project.qscz.aba.mapper.AbaPlanProjectGoalMapper">
    <resultMap id="AbaPlanProjectGoalMap" type="com.songbai.qscz.project.qscz.aba.model.AbaPlanProjectGoal">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orgId" column="org_id" jdbcType="INTEGER"/>
        <result property="childId" column="child_id" jdbcType="INTEGER"/>
        <result property="planId" column="plan_id" jdbcType="INTEGER"/>
        <result property="domainId" column="domain_id" jdbcType="INTEGER"/>
        <result property="planProjectId" column="plan_project_id" jdbcType="INTEGER"/>
        <result property="projectId" column="project_id" jdbcType="INTEGER"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        <result property="shortGoalId" column="short_goal_id" jdbcType="INTEGER"/>
        <result property="shortGoalName" column="short_goal_name" jdbcType="VARCHAR"/>
        <result property="assistMethod" column="assist_method" jdbcType="VARCHAR"/>
        <result property="target" column="target" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="recordType" column="record_type" jdbcType="INTEGER"/>
        <result property="optType" column="opt_type" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
    </resultMap>
</mapper>

