<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.songbai.qscz.project.qscz.aba.mapper.AbaCourseRecordMapper">
    <resultMap id="AbaCourseRecordMap" type="com.songbai.qscz.project.qscz.aba.model.AbaCourseRecord">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="courseId" column="course_id" jdbcType="INTEGER"/>
        <result property="courseProjectId" column="course_project_id" jdbcType="INTEGER"/>
        <result property="projectId" column="project_id" jdbcType="INTEGER"/>
        <result property="targetName" column="target_name" jdbcType="VARCHAR"/>
        <result property="targetColor" column="target_color" jdbcType="VARCHAR"/>
        <result property="targetDone" column="target_done" jdbcType="INTEGER"/>
        <result property="assistMethod" column="assist_method" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="courseProjectGoalId" column="course_project_goal_id" jdbcType="INTEGER"/>
        <result property="shortGoalId" column="short_goal_id" jdbcType="INTEGER"/>
        <result property="planProjectGoalId" column="plan_project_goal_id" jdbcType="INTEGER"/>
    </resultMap>
</mapper>

