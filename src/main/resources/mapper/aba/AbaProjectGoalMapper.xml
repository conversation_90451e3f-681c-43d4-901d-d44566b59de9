<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.songbai.qscz.project.qscz.aba.mapper.AbaProjectGoalMapper">
    <resultMap id="AbaProjectGoalMap" type="com.songbai.qscz.project.qscz.aba.model.AbaProjectGoal">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="projectId" column="project_id" jdbcType="INTEGER"/>
        <result property="shortGoal" column="short_goal" jdbcType="VARCHAR"/>
        <result property="operateMethod" column="operate_method" jdbcType="VARCHAR"/>
        <result property="operateVideo" column="operate_video" jdbcType="VARCHAR"/>
        <result property="notice" column="notice" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
    </resultMap>
</mapper>

