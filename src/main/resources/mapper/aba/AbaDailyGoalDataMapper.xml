<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.songbai.qscz.project.qscz.aba.mapper.AbaDailyGoalDataMapper">
  <resultMap id="BaseResultMap" type="com.songbai.qscz.project.qscz.aba.model.AbaDailyGoalData">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="plan_project_id" jdbcType="INTEGER" property="planProjectId" />
    <result column="plan_project_goal_id" jdbcType="INTEGER" property="planProjectGoalId" />
    <result column="short_goal_id" jdbcType="INTEGER" property="shortGoalId" />
    <result column="short_goal_name" jdbcType="VARCHAR" property="shortGoalName" />
    <result column="assist_method" jdbcType="VARCHAR" property="assistMethod" />
    <result column="target_result" jdbcType="LONGVARCHAR" property="targetResult" />
    <result column="record_type" jdbcType="INTEGER" property="recordType" />
    <result column="course_ids" jdbcType="VARCHAR" property="courseIds" />
    <result column="course_project_ids" jdbcType="VARCHAR" property="courseProjectIds" />
    <result column="course_project_goal_ids" jdbcType="VARCHAR" property="courseProjectGoalIds" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_level" jdbcType="INTEGER" property="projectLevel" />
    <result column="pass_rate" jdbcType="DOUBLE" property="passRate" />
    <result column="is_continuous_pass" jdbcType="INTEGER" property="isContinuousPass" />
    <result column="is_fail" jdbcType="INTEGER" property="isFail" />
    <result column="is_slow" jdbcType="INTEGER" property="isSlow" />
    <result column="is_step_pass" jdbcType="INTEGER" property="isStepPass" />
    <result column="try_item_total" jdbcType="INTEGER" property="tryItemTotal" />
    <result column="try_pass_count" jdbcType="INTEGER" property="tryPassCount" />

  </resultMap>

</mapper>
