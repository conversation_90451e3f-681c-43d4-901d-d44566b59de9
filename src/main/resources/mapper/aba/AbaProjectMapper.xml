<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.songbai.qscz.project.qscz.aba.mapper.AbaProjectMapper">
    <resultMap id="AbaProjectMap" type="com.songbai.qscz.project.qscz.aba.model.AbaProject">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="domainId" column="domain_id" jdbcType="INTEGER"/>
        <result property="domainName" column="domain_name" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="INTEGER"/>
        <result property="longGoal" column="long_goal" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
    </resultMap>
</mapper>

