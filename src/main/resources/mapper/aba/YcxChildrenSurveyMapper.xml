<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.songbai.qscz.project.qscz.aba.mapper.YcxChildrenSurveyMapper">
    <resultMap id="YcxChildrenSurveyMap" type="com.songbai.qscz.project.qscz.aba.model.YcxChildrenSurvey">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="childrenId" column="children_id" jdbcType="INTEGER"/>
        <result property="residence" column="residence" jdbcType="VARCHAR"/>
        <result property="primaryCaregiver" column="primary_caregiver" jdbcType="VARCHAR"/>
        <result property="fatherBirthAge" column="father_birth_age" jdbcType="INTEGER"/>
        <result property="fatherResidence" column="father_residence" jdbcType="VARCHAR"/>
        <result property="fatherProfession" column="father_profession" jdbcType="VARCHAR"/>
        <result property="motherBirthAge" column="mother_birth_age" jdbcType="INTEGER"/>
        <result property="motherProfession" column="mother_profession" jdbcType="VARCHAR"/>
        <result property="motherResidence" column="mother_residence" jdbcType="VARCHAR"/>
        <result property="familyMedicalHistory" column="family_medical_history" jdbcType="VARCHAR"/>
        <result property="birthCondition" column="birth_condition" jdbcType="VARCHAR"/>
        <result property="growthHistory" column="growth_history" jdbcType="VARCHAR"/>
        <result property="vision" column="vision" jdbcType="VARCHAR"/>
        <result property="hearing" column="hearing" jdbcType="VARCHAR"/>
        <result property="medicalDiagnosis" column="medical_diagnosis" jdbcType="VARCHAR"/>
        <result property="comorbidCondition" column="comorbid_condition" jdbcType="VARCHAR"/>
        <result property="seriousInjury" column="serious_injury" jdbcType="VARCHAR"/>
        <result property="isSchool" column="is_school" jdbcType="VARCHAR"/>
        <result property="reinforcementFood" column="reinforcement_food" jdbcType="VARCHAR"/>
        <result property="reinforcementToy" column="reinforcement_toy" jdbcType="VARCHAR"/>
        <result property="reinforcementActivity" column="reinforcement_activity" jdbcType="VARCHAR"/>
        <result property="reinforcementOther" column="reinforcement_other" jdbcType="VARCHAR"/>
        <result property="resistanceFear" column="resistance_fear" jdbcType="VARCHAR"/>
        <result property="rehabilitationHistory" column="rehabilitation_history" jdbcType="VARCHAR"/>
        <result property="rehabilitationGoal" column="rehabilitation_goal" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
</mapper>

