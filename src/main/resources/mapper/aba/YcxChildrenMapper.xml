<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.songbai.qscz.project.qscz.aba.mapper.YcxChildrenMapper">
    <resultMap id="YcxChildrenMap" type="com.songbai.qscz.project.qscz.aba.model.YcxChildren">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="erpPatientId" column="erp_patient_id" jdbcType="INTEGER"/>
        <result property="idCard" column="id_card" jdbcType="VARCHAR"/>
        <result property="childNo" column="child_no" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="guardian" column="guardian" jdbcType="VARCHAR"/>
        <result property="logo" column="logo" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="INTEGER"/>
        <result property="birthday" column="birthday" jdbcType="TIMESTAMP"/>
        <result property="nation" column="nation" jdbcType="VARCHAR"/>
        <result property="isCity" column="is_city" jdbcType="INTEGER"/>
        <result property="linkMobile" column="link_mobile" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="pregnantWeek" column="pregnant_week" jdbcType="VARCHAR"/>
        <result property="province" column="province" jdbcType="INTEGER"/>
        <result property="city" column="city" jdbcType="INTEGER"/>
        <result property="area" column="area" jdbcType="INTEGER"/>
        <result property="orgId" column="org_id" jdbcType="INTEGER"/>
        <result property="isLeave" column="is_leave" jdbcType="INTEGER"/>
        <result property="isCourseCompleted" column="is_course_completed" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="INTEGER"/>
        <result property="createName" column="create_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
    </resultMap>
</mapper>

