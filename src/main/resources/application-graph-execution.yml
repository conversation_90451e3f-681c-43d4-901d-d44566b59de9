# 图执行配置示例
# 在 application.yml 中引入此配置文件：spring.profiles.include: graph-execution

graph:
  execution:
    # 是否启用流式输出（设为false使用简化执行器）
    enable-streaming-output: false
    
    # 线程池配置
    default-thread-pool-size: 10
    max-thread-pool-size: 50
    thread-pool-queue-capacity: 1000
    thread-keep-alive-seconds: 60
    
    # 默认隔离级别
    default-isolation-level: THREAD_LEVEL
    
    # 超时配置
    execution-timeout-minutes: 30
    
    # 数据保留配置
    state-history-retention-days: 7
    execution-result-retention-minutes: 1440  # 24小时
    isolation-context-timeout-minutes: 60
    
    # 自动清理配置
    enable-auto-cleanup: true
    auto-cleanup-interval-minutes: 60
    
    # 并发控制
    max-concurrent-executions: 100
    
    # 功能开关
    enable-execution-statistics: true
    enable-state-listening: true
    
    # 线程池详细配置
    thread-pool:
      name-prefix: "graph-executor"
      daemon: false
      rejected-execution-policy: "CALLER_RUNS"  # ABORT, DISCARD, DISCARD_OLDEST, CALLER_RUNS
    
    # 监控配置
    monitoring:
      enable-metrics: true
      enable-health-check: true
      metrics-collection-interval-seconds: 60

# 日志配置
logging:
  level:
    com.songbai.qscz.common.ai.graph: DEBUG
    com.songbai.qscz.common.ai.graph.execution: INFO
    com.songbai.qscz.common.ai.graph.isolation: DEBUG
    com.songbai.qscz.common.ai.graph.state: DEBUG
    com.songbai.qscz.common.ai.graph.threadpool: INFO

# Spring Boot Actuator 配置（可选）
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,graph-execution
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      simple:
        enabled: true

# 示例：不同环境的配置
---
spring:
  profiles: development
graph:
  execution:
    default-thread-pool-size: 5
    max-thread-pool-size: 20
    execution-timeout-minutes: 10
    enable-auto-cleanup: true
    auto-cleanup-interval-minutes: 30

---
spring:
  profiles: production
graph:
  execution:
    default-thread-pool-size: 20
    max-thread-pool-size: 100
    execution-timeout-minutes: 60
    enable-auto-cleanup: true
    auto-cleanup-interval-minutes: 120
    monitoring:
      enable-metrics: true
      enable-health-check: true

---
spring:
  profiles: test
graph:
  execution:
    default-thread-pool-size: 2
    max-thread-pool-size: 5
    execution-timeout-minutes: 5
    enable-auto-cleanup: false
    enable-execution-statistics: false
    enable-state-listening: false
